{"name": "mobile", "version": "1.0.0", "main": "index.ts", "scripts": {"dev": "node start-enterprise.js", "start": "node start-enterprise.js", "android": "node start-enterprise.js --android", "ios": "node start-enterprise.js --ios", "web": "node start-enterprise.js --web", "start-enterprise": "node start-enterprise.js", "start-safe": "node fix-expo-cache.js", "start-fallback": "copy metro.config.fallback.js metro.config.js && node fix-expo-cache.js", "start-original": "npx expo start", "cache:validate": "node scripts/enterprise-cache-manager.js validate", "cache:clean": "node scripts/enterprise-cache-manager.js clean", "cache:optimize": "node scripts/enterprise-cache-manager.js optimize", "cache:reset": "node scripts/enterprise-cache-manager.js full-reset", "cache:legacy-clean": "node scripts/metro-cache-manager.js clean", "cache:legacy-validate": "node scripts/metro-cache-manager.js validate", "cache:legacy-fix-deps": "node scripts/metro-cache-manager.js fix-deps", "clear-cache": "npm run cache:clean && npm start", "reset-metro": "npm run cache:reset && npm start", "type-check": "tsc --noEmit", "build:android:apk": "npx expo prebuild --platform android && cd android && ./gradlew assembleRelease", "build:android:aab": "npx expo prebuild --platform android && cd android && ./gradlew bundleRelease", "build:android:debug": "npx expo prebuild --platform android && cd android && ./gradlew assembleDebug", "prebuild:android": "npx expo prebuild --platform android --clean", "prebuild:clean": "npx expo prebuild --clear"}, "dependencies": {"@babel/runtime": "^7.25.0", "@expo/metro-runtime": "~5.0.4", "@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-firebase/app": "^22.2.1", "@react-native-firebase/auth": "^22.2.1", "@react-native-firebase/firestore": "^22.2.1", "@react-navigation/bottom-tabs": "^7.3.17", "@react-navigation/native": "^7.1.13", "@react-navigation/stack": "^7.3.6", "expo": "~53.0.12", "expo-asset": "^11.1.5", "expo-constants": "^17.1.6", "expo-file-system": "^18.1.10", "expo-font": "^13.3.1", "expo-linear-gradient": "~14.1.5", "expo-router": "^5.1.0", "expo-status-bar": "~2.2.3", "fbjs": "^3.0.5", "firebase": "^11.8.1", "nativewind": "^2.0.11", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.4", "react-native-maps": "^1.20.1", "react-native-reanimated": "3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "^4.11.1", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-web": "^0.20.0", "react-native-web-linear-gradient": "^1.1.2", "typescript": "~5.8.3"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/cli": "^0.24.15", "@types/react": "~19.0.10", "prettier-plugin-tailwindcss": "^0.5.14", "tailwindcss": "3.3.2", "typescript": "~5.8.3"}, "private": true}